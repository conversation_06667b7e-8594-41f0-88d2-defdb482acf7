{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/opt/ros/humble/include/**", "/usr/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/**"], "defines": ["ROS2", "HUMBLE"], "compilerPath": "/usr/bin/gcc", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64", "configurationProvider": "ms-vscode.cmake-tools", "compileCommands": "${workspaceFolder}/build/compile_commands.json", "browse": {"path": ["${workspaceFolder}", "/opt/ros/humble/include/**", "/usr/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/**"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.c_cpp.db"}}], "version": 4}