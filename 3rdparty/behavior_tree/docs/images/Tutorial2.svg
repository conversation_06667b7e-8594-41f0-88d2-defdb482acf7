<svg xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xhtml="http://www.w3.org/1999/xhtml" style="background-color:#fff" id="svg220" width="901" height="601" version="1.1" viewBox="-0.5 -0.5 901 601"><metadata id="metadata226"/><g id="g212"><path id="path2" fill="none" stroke="#000" stroke-miterlimit="10" d="M 350 50 L 284.83 105.86" pointer-events="stroke"/><path id="path4" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 280.85 109.27 L 283.89 102.06 L 284.83 105.86 L 288.44 107.37 Z" pointer-events="all"/><path id="path6" fill="none" stroke="#000" stroke-miterlimit="10" d="M 350 50 L 410.32 105.68" pointer-events="stroke"/><path id="path8" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 414.18 109.24 L 406.66 107.07 L 410.32 105.68 L 411.41 101.92 Z" pointer-events="all"/><path id="path10" fill="none" stroke="#000" stroke-miterlimit="10" d="M 350 50 L 543.9 108.17" pointer-events="stroke"/><path id="path12" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 548.93 109.68 L 541.22 111.02 L 543.9 108.17 L 543.23 104.31 Z" pointer-events="all"/><path id="path14" fill="none" stroke="#000" stroke-miterlimit="10" d="M 350 50 L 91.21 108.59" pointer-events="stroke"/><path id="path16" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 86.09 109.75 L 92.14 104.79 L 91.21 108.59 L 93.69 111.62 Z" pointer-events="all"/><rect id="rect18" width="120" height="40" x="290" y="10" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g24" transform="translate(-0.5 -0.5)"><switch id="switch22"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:30px;margin-left:291px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">Sequence</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text20" x="350" y="35" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">Sequence</text></switch></g><rect id="rect26" width="120" height="40" x="190" y="110" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g32" transform="translate(-0.5 -0.5)"><switch id="switch30"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:130px;margin-left:191px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">OpenGripper</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text28" x="250" y="135" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">OpenGripper</text></switch></g><rect id="rect34" width="150" height="40" x="340" y="110" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g40" transform="translate(-0.5 -0.5)"><switch id="switch38"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:148px;height:1px;padding-top:130px;margin-left:341px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">ApproachObject</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text36" x="415" y="135" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">ApproachObject</text></switch></g><rect id="rect42" width="120" height="40" x="520" y="110" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g48" transform="translate(-0.5 -0.5)"><switch id="switch46"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:130px;margin-left:521px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">CloseGripper</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text44" x="580" y="135" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">CloseGripper</text></switch></g><rect id="rect50" width="150" height="40" x="10" y="110" fill="#FFF" stroke="#000" pointer-events="all" rx="14.4" ry="14.4"/><g id="g56" transform="translate(-0.5 -0.5)"><switch id="switch54"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:148px;height:1px;padding-top:130px;margin-left:11px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">CheckBattery</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text52" x="85" y="135" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">CheckBattery</text></switch></g><path id="path58" fill="none" stroke="#000" stroke-miterlimit="10" d="M 470 224 L 608.6 280.59" pointer-events="stroke"/><path id="path60" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 613.46 282.58 L 605.66 283.17 L 608.6 280.59 L 608.31 276.69 Z" pointer-events="all"/><path id="path62" fill="none" stroke="#000" stroke-miterlimit="10" d="M 470 224 L 380.41 279.64" pointer-events="stroke"/><path id="path64" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 375.95 282.41 L 380.05 275.74 L 380.41 279.64 L 383.74 281.69 Z" pointer-events="all"/><path id="path66" fill="none" stroke="#000" stroke-miterlimit="10" d="M 470 224 L 768.75 281.79" pointer-events="stroke"/><path id="path68" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 773.9 282.79 L 766.37 284.89 L 768.75 281.79 L 767.69 278.02 Z" pointer-events="all"/><path id="path70" fill="none" stroke="#000" stroke-miterlimit="10" d="M 470 224 L 186.24 281.73" pointer-events="stroke"/><path id="path72" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 181.1 282.78 L 187.26 277.95 L 186.24 281.73 L 188.65 284.81 Z" pointer-events="all"/><rect id="rect74" width="120" height="40" x="410" y="184" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g80" transform="translate(-0.5 -0.5)"><switch id="switch78"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:118px;height:1px;padding-top:204px;margin-left:411px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal">Sequence</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text76" x="470" y="209" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">Sequence</text></switch></g><path id="path82" fill="none" stroke="#000" stroke-dasharray="3 3" stroke-miterlimit="10" d="M 574.5 359 Q 574.5 430 522.25 430 Q 470 430 470 485 Q 470 540 513.63 540" pointer-events="stroke"/><path id="path84" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 518.88 540 L 511.88 543.5 L 513.63 540 L 511.88 536.5 Z" pointer-events="all"/><rect id="rect86" width="160" height="76" x="494.5" y="283" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g92" transform="translate(-0.5 -0.5)"><switch id="switch90"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:158px;height:1px;padding-top:321px;margin-left:496px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal"><xhtml:div>ThinkWhatToSay</xhtml:div><xhtml:div style="font-size:15px"><xhtml:br/></xhtml:div><xhtml:div><xhtml:font style="font-size:15px">text={the_answer}</xhtml:font><xhtml:br/></xhtml:div></xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text88" x="575" y="326" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">ThinkWhatToSay...</text></switch></g><rect id="rect94" width="190" height="76" x="280" y="283" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g100" transform="translate(-0.5 -0.5)"><switch id="switch98"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:321px;margin-left:281px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal"><xhtml:div>SaySomething2</xhtml:div><xhtml:div style="font-size:15px"><xhtml:br/></xhtml:div><xhtml:div style="font-size:15px">message=&quot;this works too&quot;<xhtml:br/></xhtml:div></xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text96" x="375" y="326" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">SaySomething2...</text></switch></g><rect id="rect102" width="190" height="76" x="680" y="283" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g108" transform="translate(-0.5 -0.5)"><switch id="switch106"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:188px;height:1px;padding-top:321px;margin-left:681px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal"><xhtml:div style="font-size:15px"><xhtml:font style="font-size:18px">SaySomething</xhtml:font><xhtml:div><xhtml:br/></xhtml:div>message={the_answer}</xhtml:div></xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text104" x="775" y="326" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">SaySomething...</text></switch></g><rect id="rect110" width="160" height="82" x="100" y="283" fill="#FFF" stroke="#000" pointer-events="all"/><g id="g116" transform="translate(-0.5 -0.5)"><switch id="switch114"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:158px;height:1px;padding-top:324px;margin-left:101px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:all;white-space:normal;overflow-wrap:normal"><xhtml:div>SaySomething</xhtml:div><xhtml:div><xhtml:br/></xhtml:div><xhtml:div style="font-size:15px">message=&quot;hello&quot;<xhtml:br/></xhtml:div></xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text112" x="180" y="329" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">SaySomething...</text></switch></g><path id="path118" fill="#FFF" stroke="#00f" stroke-miterlimit="10" d="M 520 490 L 520 460 L 850 460 L 850 490" pointer-events="all"/><path id="path120" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 520 490 L 520 590 L 850 590 L 850 490" pointer-events="none"/><path id="path122" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 520 490 L 850 490" pointer-events="none"/><path id="path124" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 520 523 L 630 523 L 710 523 L 850 523" pointer-events="none"/><path id="path126" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 520 557 L 630 557 L 710 557 L 850 557" pointer-events="none"/><path id="path128" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 630 490 L 630 523 L 630 557 L 630 590" pointer-events="none"/><path id="path130" fill="none" stroke="#00f" stroke-miterlimit="10" d="M 710 490 L 710 523 L 710 557 L 710 590" pointer-events="none"/><g id="g134" fill="#000" font-family="Helvetica" font-size="18" font-weight="bold" pointer-events="none" text-anchor="middle"><text id="text132" x="684.5" y="482.5">Blackboard</text></g><path id="path136" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 520 490 M 630 490 M 630 523 M 520 523" pointer-events="none"/><g id="g142" transform="translate(-0.5 -0.5)"><switch id="switch140"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:108px;height:1px;padding-top:507px;margin-left:521px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:16px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;font-weight:700;white-space:normal;overflow-wrap:normal">KEY</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text138" x="575" y="511" fill="#000" font-family="Helvetica" font-size="16" font-weight="bold" text-anchor="middle">KEY</text></switch></g><path id="path144" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 630 490 M 710 490 M 710 523 M 630 523" pointer-events="none"/><g id="g150" transform="translate(-0.5 -0.5)"><switch id="switch148"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:78px;height:1px;padding-top:507px;margin-left:631px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:16px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;font-weight:700;white-space:normal;overflow-wrap:normal">TYPE</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text146" x="670" y="511" fill="#000" font-family="Helvetica" font-size="16" font-weight="bold" text-anchor="middle">TYPE</text></switch></g><path id="path152" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 710 490 M 850 490 M 850 523 M 710 523" pointer-events="none"/><g id="g158" transform="translate(-0.5 -0.5)"><switch id="switch156"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:138px;height:1px;padding-top:507px;margin-left:711px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:16px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;font-weight:700;white-space:normal;overflow-wrap:normal">VALUE</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text154" x="780" y="511" fill="#000" font-family="Helvetica" font-size="16" font-weight="bold" text-anchor="middle">VALUE</text></switch></g><path id="path160" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 520 523 M 630 523 M 630 557 M 520 557" pointer-events="none"/><g id="g166" transform="translate(-0.5 -0.5)"><switch id="switch164"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:108px;height:1px;padding-top:540px;margin-left:521px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:30px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:15px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">the_answer</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text162" x="575" y="545" fill="#000" font-family="Helvetica" font-size="15" text-anchor="middle">the_answer</text></switch></g><path id="path168" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 630 523 M 710 523 M 710 557 M 630 557" pointer-events="none"/><g id="g174" transform="translate(-0.5 -0.5)"><switch id="switch172"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:78px;height:1px;padding-top:540px;margin-left:631px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:30px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:15px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">string</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text170" x="670" y="545" fill="#000" font-family="Helvetica" font-size="15" text-anchor="middle">string</text></switch></g><path id="path176" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 710 523 M 850 523 M 850 557 M 710 557" pointer-events="none"/><g id="g182" transform="translate(-0.5 -0.5)"><switch id="switch180"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:138px;height:1px;padding-top:540px;margin-left:711px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:30px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:15px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal"><xhtml:font style="font-size:15px">&quot;the answer is 42&quot;<xhtml:br style="font-size:15px"/></xhtml:font></xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text178" x="780" y="545" fill="#000" font-family="Helvetica" font-size="15" text-anchor="middle">&quot;the answer is 42&quot;</text></switch></g><path id="path184" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 520 557 M 630 557 M 630 590 M 520 590" pointer-events="none"/><g id="g190" transform="translate(-0.5 -0.5)"><switch id="switch188"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:108px;height:1px;padding-top:574px;margin-left:521px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">...</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text186" x="575" y="579" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">...</text></switch></g><path id="path192" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 630 557 M 710 557 M 710 590 M 630 590" pointer-events="none"/><g id="g198" transform="translate(-0.5 -0.5)"><switch id="switch196"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:78px;height:1px;padding-top:574px;margin-left:631px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">...</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text194" x="670" y="579" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">...</text></switch></g><path id="path200" fill="none" stroke="#00f" stroke-linecap="square" stroke-miterlimit="10" d="M 710 557 M 850 557 M 850 590 M 710 590" pointer-events="none"/><g id="g206" transform="translate(-0.5 -0.5)"><switch id="switch204"><foreignObject width="100%" height="100%" pointer-events="none" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow:visible;text-align:left"><xhtml:div style="display:flex;align-items:unsafe center;justify-content:unsafe center;width:138px;height:1px;padding-top:574px;margin-left:711px"><xhtml:div data-drawio-colors="color: rgb(0, 0, 0);" style="box-sizing:border-box;font-size:0;text-align:center;max-height:29px;overflow:hidden"><xhtml:div style="display:inline-block;font-size:18px;font-family:Helvetica;color:#000;line-height:1.2;pointer-events:none;white-space:normal;overflow-wrap:normal">...</xhtml:div></xhtml:div></xhtml:div></foreignObject><text id="text202" x="780" y="579" fill="#000" font-family="Helvetica" font-size="18" text-anchor="middle">...</text></switch></g><path id="path208" fill="none" stroke="#000" stroke-dasharray="3 3" stroke-miterlimit="10" d="M 850 540 Q 890 540 890 480 Q 890 420 832.5 420 Q 775 420 775 365.37" pointer-events="none"/><path id="path210" fill="#000" stroke="#000" stroke-miterlimit="10" d="M 775 360.12 L 778.5 367.12 L 775 365.37 L 771.5 367.12 Z" pointer-events="none"/></g></svg>