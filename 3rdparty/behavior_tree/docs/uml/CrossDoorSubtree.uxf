<?xml version="1.0" encoding="UTF-8"?><diagram program="umlet" version="13.3">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>270</x>
      <y>140</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Fallback
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>270</x>
      <y>540</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>OpenDoor</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>380</x>
      <y>220</y>
      <w>160</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>PassThroughWindow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>160</y>
      <w>150</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>130.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>250</x>
      <y>460</y>
      <w>150</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>RetryUntilSuccesfful
(num_attempts=4)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>110</y>
      <w>30</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>70</x>
      <y>280</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>isDoorOpen?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>160</y>
      <w>150</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;60.0;130.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>410</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>410</y>
      <w>150</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;60.0;130.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>270</x>
      <y>390</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Sequence
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>160</x>
      <y>470</y>
      <w>80</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Inverter</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLUseCase</id>
    <coordinates>
      <x>140</x>
      <y>530</y>
      <w>120</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>isDoorOpen?</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>490</y>
      <w>30</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>260</x>
      <y>340</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>*DoorClosed*
bg=gray</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>360</y>
      <w>30</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>260</x>
      <y>90</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>*MainTree*
bg=gray

</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>490</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>410</x>
      <y>470</y>
      <w>140</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>PassThroughDoor</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>410</y>
      <w>190</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>170.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>150</x>
      <y>220</y>
      <w>100</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Sequence
</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>200</x>
      <y>280</y>
      <w>140</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>PassThroughDoor</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>240</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>80.0;10.0;10.0;40.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>190</x>
      <y>240</y>
      <w>100</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;80.0;40.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>260</x>
      <y>210</y>
      <w>110</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>Subtree:
*DoorClosed*
fg=blue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>310</x>
      <y>160</y>
      <w>30</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
