# RSlamware Encryption System - Docker Build Guide

This guide explains how to build the RSlamware encryption system using Docker.

## Prerequisites

1. Docker installed and running
2. Access to the Slamtec Docker registry: `harbor.eng.slamtec.com`
3. The rslamware workspace with the encryption system

## Quick Start

### 1. Start the Docker Container

From the rslamware workspace root directory, run:

```bash
docker run --name rslamware-builder -v ./:/root/rslamware -it harbor.eng.slamtec.com/slamware/rslamware-builder:amd64 bash
```

This command:
- Creates a container named `rslamware-builder`
- Mounts the current directory to `/root/rslamware` inside the container
- Starts an interactive bash session

### 2. Build Inside the Container

Once inside the container, navigate to the workspace and run the build script:

```bash
cd /root/rslamware
./scripts/docker_build_rslamware_encryption_run.sh
```

### 3. Exit the Container

After the build completes successfully:

```bash
exit
```

## What the Build Script Does

The `docker_build_rslamware_encryption_run.sh` script performs the following steps:

1. **Environment Setup**: Sources ROS2 Humble environment
2. **Clean Build**: Removes previous build artifacts
3. **Package Build**: Builds all ROS2 packages using colcon
4. **File Preparation**: Runs the preparation script to:
   - Copy launch and config files
   - Combine config files into a single YAML
   - Refine launch files to use the combined config
   - Create a compressed archive
5. **Encryption**: Encrypts the archive using AES-256-CBC
6. **Cleanup**: Removes intermediate files

## Output Files

After successful build:
- `rslamware.enc` - The encrypted launch and config archive
- `install/` - Built ROS2 packages
- Run scripts in `scripts/` directory

## Running the Encrypted System

### On Real Robot

```bash
# For mapping mode
./scripts/run_rslamware_encryption_run.sh mapping

# For localization mode  
./scripts/run_rslamware_encryption_run.sh localization
```

### In Simulator

```bash
# For mapping mode in simulator
./scripts/run_rslamware_encryption_run_simulator.sh mapping

# For localization mode in simulator
./scripts/run_rslamware_encryption_run_simulator.sh localization
```

## Troubleshooting

### Container Access Issues

If you get permission errors accessing the Docker registry:

```bash
docker login harbor.eng.slamtec.com
```

### Build Failures

1. Check that all dependencies are available in the container
2. Ensure the workspace structure is correct
3. Verify that OpenSSL development libraries are installed

### File Permission Issues

If you encounter permission issues with the mounted volume:

```bash
# Inside the container, fix permissions
chown -R root:root /root/rslamware
```

## Container Management

### Reusing the Container

If you need to rebuild, you can reuse the existing container:

```bash
# Start the existing container
docker start rslamware-builder

# Attach to it
docker exec -it rslamware-builder bash

# Navigate and rebuild
cd /root/rslamware
./scripts/docker_build_rslamware_encryption_run.sh
```

### Cleaning Up

To remove the container when done:

```bash
docker rm rslamware-builder
```

## Security Notes

- The encryption uses AES-256-CBC with a hard-coded key
- Decrypted files are kept in memory and secure temporary directories
- Temporary directories are automatically cleaned up
- The encrypted file (`rslamware.enc`) contains all launch and config data

## Architecture Overview

```
rslamware/
├── src/rslamware_encryption_run/     # Encryption package
│   ├── src/
│   │   ├── rslamware_encryption_run.cpp      # C++ encryption tool
│   │   └── rslamware_encryption_prepare.py   # Python preparation script
│   ├── CMakeLists.txt
│   └── package.xml
├── scripts/
│   ├── docker_build_rslamware_encryption_run.sh    # Docker build script
│   ├── run_rslamware_encryption_run.sh             # Real robot run script
│   └── run_rslamware_encryption_run_simulator.sh   # Simulator run script
└── rslamware.enc                     # Encrypted output (after build)
```

The system provides a secure way to distribute and run ROS2 launch configurations while protecting sensitive configuration data through encryption.
