slam_toolbox:
  ros__parameters:
    solver_plugin: solver_plugins::<PERSON><PERSON><PERSON><PERSON><PERSON>
    ceres_linear_solver: SPARSE_NORMAL_CHOLESKY
    ceres_preconditioner: SCHUR_JACOBI
    ceres_trust_strategy: LEVENBERG_MARQUARDT
    ceres_dogleg_type: TRADITIONAL_DOGLEG
    ceres_loss_function: None
    odom_frame: odom
    map_frame: map
    base_frame: base_link
    scan_topic: /fused_scan
    single_scan_topic: /scan
    use_fusion_scan: false
    use_map_saver: true
    mode: mapping
    debug_logging: false
    throttle_scans: 1
    transform_publish_period: 0.02
    map_update_interval: 0.5
    resolution: 0.05
    min_laser_range: 0.0
    max_laser_range: 20.0
    minimum_time_interval: 0.1
    transform_timeout: 0.2
    tf_buffer_duration: 30.0
    stack_size_to_use: 40000000
    enable_interactive_mode: true
    use_scan_matching: true
    use_scan_barycenter: true
    minimum_travel_distance: 0.2
    minimum_travel_heading: 0.174
    scan_buffer_size: 70
    scan_buffer_maximum_scan_distance: 20.0
    link_match_minimum_response_fine: 0.6
    link_scan_maximum_distance: 10.0
    loop_search_maximum_distance: 8.0
    do_loop_closing: true
    loop_match_minimum_chain_size: 10
    loop_match_maximum_variance_coarse: 0.16
    loop_match_minimum_response_coarse: 0.3
    loop_match_minimum_response_fine: 0.8
    correlation_search_space_dimension: 0.3
    correlation_search_space_resolution: 0.01
    correlation_search_space_smear_deviation: 0.03
    loop_search_space_dimension: 8.0
    loop_search_space_resolution: 0.05
    loop_search_space_smear_deviation: 0.03
    distance_variance_penalty: 0.09
    angle_variance_penalty: 0.1225
    fine_search_angle_offset: 0.00349
    coarse_search_angle_offset: 0.52
    coarse_angle_resolution: 0.0349
    minimum_angle_penalty: 0.7
    minimum_distance_penalty: 0.4
    use_response_expansion: true
    min_pass_through: 2
    occupancy_threshold: 0.1
jack_control:
  ros__parameters:
    enable: true
    can_interface: can0
    control_cycle_ms: 50
    response_timeout_ms: 1000
    heartbeat_period_ms: 100
    min_send_interval_ms: 5
    default_speed: 1000
    max_speed: 3000
    max_position: 26000000
    min_position: 0
    position_tolerance: 1000
    position_timeout_s: 60
    detection_timeout_s: 30
    up_stage_timeout_s: 90
    down_stage_timeout_s: 90
    base_stage_timeout_s: 45
    movement_start_delay_s: 3
    movement_check_tolerance: 100
    status_update_delay_s: 2
    auto_detect_base_on_start: true
topics:
  ros__parameters:
    front_laser_scan: lidar_front/scan
    back_laser_scan: lidar_back/scan
    filtered_front_laser_scan: lidar_front/scan_filtered
    filtered_back_laser_scan: lidar_back/scan_filtered
    undistortion_front_laser_scan: lidar_front/scan_undistortion
    undistortion_back_laser_scan: lidar_back/scan_undistortion
    fusion_scan: fusion_scan
    cmd: cmd_vel
imu_sensor:
  ros__parameters:
    gyro_device_path: /dev/iio:device1
    accel_device_path: /dev/iio:device2
    imu_frame_id: imu_link
    publish_period_ms: 10
    imu_topic: sl_vcu_all/imu_data_raw
    poll_timeout: 1000
    imu_sensor_acc_sensitivity: 4
    imu_sensor_gyro_sensitivity: 2000
    timestamp_sync_tolerance_ns: 10000000
    initial_bias_offset: 0.1
    bias_calculation_time: 10.0
    bias_update_time: 5.0
    bias_update_threshold: 0.01
    cmd_vel_timeout: 2.0
    cmd_vel_topic: cmd_vel
    imu_filtered_topic: sl_vcu_all/imu_data_filtered
    publish_tf: false
    parent_frame_id: base_link
    child_frame_id: imu_link
bumper_sensor:
  ros__parameters:
    input_device_path: /dev/input/event2
    poll_timeout_ms: 1000
    front_bumper_code: 59
    back_bumper_code: 60
    triggered_value: 1
    publish_rate_ms: 20
    bumper_topic: bumper_state
laser_filter_front:
  ros__parameters:
    filter1:
      name: angle
      type: laser_filters/LaserScanAngularBoundsFilterInPlace
      params:
        lower_angle: -0.99
        upper_angle: 0.99
        replace_with_nan: true
    filter2:
      name: median_spatial
      type: laser_filters/LaserScanMedianSpatialFilter
      params:
        window_size: 15
    filter3:
      name: shadows
      type: laser_filters/ScanShadowsFilter
      params:
        min_angle: 10.0
        max_angle: 170.0
        neighbors: 0
        window: 1
laser_filter_back:
  ros__parameters:
    filter1:
      name: angle
      type: laser_filters/LaserScanAngularBoundsFilterInPlace
      params:
        lower_angle: -1.05
        upper_angle: 1.05
        replace_with_nan: true
    filter2:
      name: median_spatial
      type: laser_filters/LaserScanMedianSpatialFilter
      params:
        window_size: 15
    filter3:
      name: shadows
      type: laser_filters/ScanShadowsFilter
      params:
        min_angle: 10.0
        max_angle: 170.0
        neighbors: 0
        window: 1
led_display_control:
  ros__parameters:
    num_channels: 2
    debug_mode: false
    auto_start_effects: true
    default_frequency: 1.0
    default_speed: 10.0
    default_marquee_direction: true
    default_green_brightness: 255
    default_red_brightness: 255
    default_blue_brightness: 255
    default_on_time_duty: 0.5
    channel_0:
      spi_device: /dev/spidev0.0
      spi_speed: 7080000
      num_leds: 10
      first_part_leds: 5
      second_part_leds: 5
      enabled: true
    channel_1:
      spi_device: /dev/spidev3.0
      spi_speed: 7080000
      num_leds: 10
      first_part_leds: 5
      second_part_leds: 5
      enabled: true
amcl:
  ros__parameters:
    use_sim_time: false
    alpha1: 0.2
    alpha2: 0.2
    alpha3: 0.8
    alpha4: 0.2
    alpha5: 0.1
    base_frame_id: base_link
    beam_skip_distance: 0.5
    beam_skip_error_threshold: 0.9
    beam_skip_threshold: 0.3
    do_beamskip: false
    global_frame_id: map
    lambda_short: 0.1
    laser_likelihood_max_dist: 2.0
    laser_max_range: 100.0
    laser_min_range: -1.0
    laser_model_type: likelihood_field
    max_beams: 60
    max_particles: 2000
    min_particles: 500
    odom_frame_id: odom
    pf_err: 0.05
    pf_z: 0.99
    recovery_alpha_fast: 0.0
    recovery_alpha_slow: 0.0
    resample_interval: 2
    robot_model_type: nav2_amcl::DifferentialMotionModel
    save_pose_rate: 0.5
    sigma_hit: 0.2
    tf_broadcast: true
    transform_tolerance: 1.0
    update_min_a: 0.2
    update_min_d: 0.2
    z_hit: 0.5
    z_max: 0.05
    z_rand: 0.5
    z_short: 0.05
    scan_topic: fusion_scan
mcl:
  ros__parameters:
    scan_name: fusion_scan
    laser_frame: base_link
    reject_unknown_scan: true
    use_gl_pose_sampler: false
    estimate_reliability: false
    use_augmented_mcl: true
    particle_num: 1000
    scan_step: 1
    z_hit: 0.95
    z_short: 0.1
    z_max: 0.01
    z_rand: 0.01
    var_hit: 0.05
    localization_hz: 20.0
    odom_noise_ddm:
    - 0.2
    - 0.1
    - 0.1
    - 0.3
    resample_thresholds:
    - 0.1
    - 0.1
    - 0.1
    - 0.017
    - -99999.0
gl_pose_sampler:
  ros__parameters:
    scan_name: fusion_scan
    laser_frame: base_link
    key_scans_num: 3
    gradient_square_th: 0.01
    average_sdf_delta_th: 0.01
    random_samples_num: 20
    keypoints_min_dist_from_map: 0.5
bt_navigator:
  ros__parameters:
    use_sim_time: false
    global_frame: map
    robot_base_frame: base_link
    odom_topic: /odom
    bt_loop_duration: 10
    transform_tolerance: 0.3
    default_server_timeout: 20
    wait_for_service_timeout: 1000
    plugin_lib_names:
    - nav2_compute_path_to_pose_action_bt_node
    - nav2_compute_path_through_poses_action_bt_node
    - nav2_smooth_path_action_bt_node
    - nav2_follow_path_action_bt_node
    - nav2_spin_action_bt_node
    - nav2_wait_action_bt_node
    - nav2_assisted_teleop_action_bt_node
    - nav2_back_up_action_bt_node
    - nav2_drive_on_heading_bt_node
    - nav2_clear_costmap_service_bt_node
    - nav2_is_stuck_condition_bt_node
    - nav2_goal_reached_condition_bt_node
    - nav2_goal_updated_condition_bt_node
    - nav2_globally_updated_goal_condition_bt_node
    - nav2_is_path_valid_condition_bt_node
    - nav2_initial_pose_received_condition_bt_node
    - nav2_reinitialize_global_localization_service_bt_node
    - nav2_rate_controller_bt_node
    - nav2_distance_controller_bt_node
    - nav2_speed_controller_bt_node
    - nav2_truncate_path_action_bt_node
    - nav2_truncate_path_local_action_bt_node
    - nav2_goal_updater_node_bt_node
    - nav2_recovery_node_bt_node
    - nav2_pipeline_sequence_bt_node
    - nav2_round_robin_node_bt_node
    - nav2_transform_available_condition_bt_node
    - nav2_time_expired_condition_bt_node
    - nav2_path_expiring_timer_condition
    - nav2_distance_traveled_condition_bt_node
    - nav2_single_trigger_bt_node
    - nav2_goal_updated_controller_bt_node
    - nav2_is_battery_low_condition_bt_node
    - nav2_navigate_through_poses_action_bt_node
    - nav2_navigate_to_pose_action_bt_node
    - nav2_remove_passed_goals_action_bt_node
    - nav2_planner_selector_bt_node
    - nav2_controller_selector_bt_node
    - nav2_goal_checker_selector_bt_node
    - nav2_controller_cancel_bt_node
    - nav2_path_longer_on_approach_bt_node
    - nav2_wait_cancel_bt_node
    - nav2_spin_cancel_bt_node
    - nav2_back_up_cancel_bt_node
    - nav2_assisted_teleop_cancel_bt_node
    - nav2_drive_on_heading_cancel_bt_node
    - nav2_is_battery_charging_condition_bt_node
    - nav2_path_monitor_bt_node
    - nav2_path_evaluator_bt_node
    - nav2_spin_to_goal_action_bt_node
bt_navigator_navigate_through_poses_rclcpp_node:
  ros__parameters:
    use_sim_time: false
bt_navigator_navigate_to_pose_rclcpp_node:
  ros__parameters:
    use_sim_time: false
controller_server:
  ros__parameters:
    use_sim_time: false
    controller_frequency: 30.0
    min_x_velocity_threshold: 0.001
    min_y_velocity_threshold: 0.0
    min_theta_velocity_threshold: 0.001
    failure_tolerance: 0.3
    transform_tolerance: 0.3
    use_realtime_priority: true
    progress_checker_plugin: progress_checker
    goal_checker_plugins:
    - general_goal_checker
    controller_plugins:
    - FollowPath
    progress_checker:
      plugin: nav2_controller::PoseProgressChecker
      required_movement_radius: 0.05
      movement_time_allowance: 6.0
    general_goal_checker:
      stateful: false
      plugin: nav2_controller::SimpleGoalChecker
      xy_goal_tolerance: 0.1
      yaw_goal_tolerance: 3.14
    FollowPath:
      plugin: nav2_rotation_shim_controller::RotationShimController
      primary_controller: dwb_core::DWBLocalPlanner
      angular_dist_threshold: 1.0
      forward_sampling_distance: 0.5
      angular_disengage_threshold: 0.5
      rotate_to_heading_angular_vel: 0.8
      max_angular_accel: 3.2
      simulate_ahead_time: 1.0
      rotate_to_goal_heading: false
      debug_trajectory_details: true
      min_vel_x: 0.0
      min_vel_y: 0.0
      max_vel_x: 1.0
      max_vel_y: 0.0
      max_vel_theta: 1.0
      min_speed_xy: 0.0
      max_speed_xy: 1.0
      min_speed_theta: 0.0
      acc_lim_x: 0.3
      acc_lim_y: 0.0
      acc_lim_theta: 3.0
      decel_lim_x: -1.0
      decel_lim_y: 0.0
      decel_lim_theta: -3.0
      vx_samples: 20
      vy_samples: 0
      vtheta_samples: 20
      sim_time: 1.2
      linear_granularity: 0.05
      angular_granularity: 0.025
      transform_tolerance: 0.3
      xy_goal_tolerance: 0.1
      trans_stopped_velocity: 0.01
      rot_stopped_velocity: 0.01
      use_differential_drive: true
      short_circuit_trajectory_evaluation: true
      stateful: true
      speed_reduce_enabled: true
      speed_reduce_min_threshold: 0.8
      critics:
      - RotateToGoal
      - Oscillation
      - BaseObstacle
      - ObstacleFootprint
      - GoalAlign
      - PathAlign
      - PathDist
      - GoalDist
      - PreferForward
      BaseObstacle.scale: 0.0
      ObstacleFootprint.scale: 0.5
      PathAlign.scale: 32.0
      PathAlign.forward_point_distance: 0.1
      GoalAlign.scale: 24.0
      GoalAlign.forward_point_distance: 0.1
      PathDist.scale: 40.0
      GoalDist.scale: 24.0
      RotateToGoal.scale: 32.0
      RotateToGoal.slowing_factor: 5.0
      RotateToGoal.lookahead_time: -1.0
      PreferForward.scale: 0.1
      Oscillation.scale: 1.0
local_costmap:
  local_costmap:
    ros__parameters:
      update_frequency: 30.0
      publish_frequency: 30.0
      global_frame: odom
      robot_base_frame: base_link
      use_sim_time: false
      rolling_window: true
      transform_tolerance: 0.3
      width: 4
      height: 4
      resolution: 0.05
      footprint: '[[-0.39, -0.245], [-0.39, 0.245], [0.39, 0.245], [0.39, -0.245]]'
      plugins:
      - obstacle_layer
      - voxel_layer
      - keepout_filter
      - denoise_layer
      - inflation_layer
      inflation_layer:
        plugin: nav2_costmap_2d::InflationLayer
        inflation_radius: 0.4
        cost_scaling_factor: 3.0
      obstacle_layer:
        plugin: nav2_costmap_2d::ObstacleLayer
        enabled: true
        laser_points_filter_enabled: true
        observation_sources: scan depth_points
        scan:
          topic: /fusion_scan
          max_obstacle_height: 1.2
          clearing: true
          marking: true
          data_type: LaserScan
          observation_persistence: 0.5
          raytrace_max_range: 2.0
          obstacle_max_range: 2.0
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: false
          marking: true
          data_type: PointCloud2
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 1.0
      voxel_layer:
        plugin: nav2_costmap_2d::VoxelLayer
        enabled: false
        publish_voxel_map: true
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 1.3
        mark_threshold: 4
        observation_sources: depth_points
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: true
          marking: true
          data_type: PointCloud2
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 0.2
      static_layer:
        plugin: nav2_costmap_2d::StaticLayer
        map_subscribe_transient_local: true
      denoise_layer:
        plugin: nav2_costmap_2d::DenoiseLayer
        enabled: true
        minimal_group_size: 3
      always_send_full_costmap: true
      keepout_filter:
        plugin: nav2_costmap_2d::KeepoutFilter
        enabled: true
        filter_info_topic: /costmap_filter_info
        transform_tolerance: 0.3
global_costmap:
  global_costmap:
    ros__parameters:
      update_frequency: 4.0
      publish_frequency: 4.0
      global_frame: map
      robot_base_frame: base_link
      use_sim_time: false
      static_map: false
      map_topic: /map
      transform_tolerance: 0.3
      footprint: '[[-0.39, -0.24], [-0.39, 0.24], [0.39, 0.24], [0.39, -0.24]]'
      resolution: 0.05
      track_unknown_space: true
      plugins:
      - static_layer
      - voxel_layer
      - obstacle_layer
      - keepout_filter
      - denoise_layer
      - inflation_layer
      obstacle_layer:
        plugin: nav2_costmap_2d::ObstacleLayer
        enabled: true
        laser_points_filter_enabled: true
        observation_sources: scan depth_points
        scan:
          topic: /fusion_scan
          max_obstacle_height: 2.0
          clearing: true
          marking: true
          data_type: LaserScan
          observation_persistence: 0.5
          raytrace_max_range: 3.0
          obstacle_max_range: 3.0
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: false
          marking: true
          data_type: PointCloud2
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 1.0
      voxel_layer:
        plugin: nav2_costmap_2d::VoxelLayer
        enabled: false
        publish_voxel_map: true
        origin_z: 0.0
        z_resolution: 0.05
        z_voxels: 16
        max_obstacle_height: 1.3
        mark_threshold: 4
        observation_sources: depth_points
        depth_points:
          topic: /depthcam/processed_cloud
          max_obstacle_height: 1.3
          clearing: true
          marking: true
          data_type: PointCloud2
          raytrace_max_range: 2.0
          raytrace_min_range: 0.0
          obstacle_max_range: 2.0
          obstacle_min_range: 0.0
          observation_persistence: 0.2
      static_layer:
        plugin: nav2_costmap_2d::StaticLayer
        map_subscribe_transient_local: true
      inflation_layer:
        plugin: nav2_costmap_2d::InflationLayer
        cost_scaling_factor: 3.0
        inflation_radius: 0.6
      denoise_layer:
        plugin: nav2_costmap_2d::DenoiseLayer
        enabled: true
        minimal_group_size: 3
      always_send_full_costmap: true
      keepout_filter:
        plugin: nav2_costmap_2d::KeepoutFilter
        enabled: true
        filter_info_topic: /costmap_filter_info
        transform_tolerance: 0.3
map_server:
  ros__parameters:
    use_sim_time: false
    yaml_filename: map.yaml
map_saver:
  ros__parameters:
    use_sim_time: false
    save_map_timeout: 5.0
    free_thresh_default: 0.25
    occupied_thresh_default: 0.65
    map_subscribe_transient_local: true
planner_server:
  ros__parameters:
    expected_planner_frequency: 5.0
    use_sim_time: false
    planner_plugins:
    - GridBased
    GridBased:
      plugin: nav2_smac_planner/SmacPlanner2D
      tolerance: 0.2
      downsample_costmap: false
      downsampling_factor: 2
      allow_unknown: false
      max_iterations: 100000
      max_on_approach_iterations: 1000
      goal_checker_name: simple_goal_checker
      angle_tolerance: 0.1
      xy_goal_tolerance: 0.25
      heuristic_type: euclidean
      heuristic_downweight: 1.0
      cost_travel_multiplier: 3.0
      smooth_path: true
      interpolation_resolution: 0.1
smoother_server:
  ros__parameters:
    use_sim_time: false
    smoother_plugins:
    - simple_smoother
    simple_smoother:
      plugin: nav2_smoother::SimpleSmoother
      tolerance: 1.0e-10
      max_its: 1000
      do_refinement: true
      cost_threshold: 100.0
      interpolation_resolution: 0.1
behavior_server:
  ros__parameters:
    costmap_topic: local_costmap/costmap_raw
    footprint_topic: local_costmap/published_footprint
    cycle_frequency: 10.0
    behavior_plugins:
    - spin
    - backup
    - drive_on_heading
    - assisted_teleop
    - wait
    spin:
      plugin: nav2_behaviors/Spin
    backup:
      plugin: pb_nav2_behaviors/BackUpFreeSpace
    drive_on_heading:
      plugin: nav2_behaviors/DriveOnHeading
    wait:
      plugin: nav2_behaviors/Wait
    assisted_teleop:
      plugin: nav2_behaviors/AssistedTeleop
    global_frame: odom
    robot_base_frame: base_link
    transform_tolerance: 0.3
    use_sim_time: false
    simulate_ahead_time: 2.0
    max_rotational_vel: 0.8
    min_rotational_vel: 0.4
    rotational_acc_lim: 3.2
    max_radius: 0.55
    service_name: local_costmap/get_costmap
    free_threshold: 5
    visualize: true
robot_state_publisher:
  ros__parameters:
    use_sim_time: false
waypoint_follower:
  ros__parameters:
    use_sim_time: false
    loop_rate: 20
    stop_on_failure: false
    waypoint_task_executor_plugin: wait_at_waypoint
    wait_at_waypoint:
      plugin: nav2_waypoint_follower::WaitAtWaypoint
      enabled: true
      waypoint_pause_duration: 200
velocity_smoother:
  ros__parameters:
    use_sim_time: false
    smoothing_frequency: 30.0
    scale_velocities: true
    feedback: OPEN_LOOP
    max_velocity:
    - 1.0
    - 0.0
    - 1.0
    min_velocity:
    - -0.2
    - 0.0
    - -1.0
    max_accel:
    - 0.5
    - 0.0
    - 5.0
    max_decel:
    - -1.0
    - 0.0
    - -5.0
    odom_topic: odom
    odom_duration: 0.1
    deadband_velocity:
    - 0.0
    - 0.0
    - 0.0
    velocity_timeout: 0.5
camera_devices:
- enable: true
  description: lower_camera
  depth_image_topic_name: /ascamera_nuwa/camera_publisher/depth0/image_raw
  camera_info_topic_name: /ascamera_nuwa/camera_publisher/depth0/camera_info
  extrinsics:
  - 0.0
  - 0.3907311
  - 0.9205049
  - 0.37371
  - -1.0
  - 0.0
  - 0.0
  - 0.0
  - -0.0
  - -0.9205049
  - 0.3907311
  - 0.07051
  - 0
  - 0
  - 0
  - 1
- enable: false
  description: upper_camera
  depth_image_topic_name: /ascamera_nuwa2/camera_publisher/depth0/image_raw
  camera_info_topic_name: /ascamera_nuwa2/camera_publisher/depth0/camera_info
  extrinsics:
  - 0.0
  - -0.8660254
  - 0.5
  - 0.38557
  - -1.0
  - -0.0
  - 0.0
  - 0
  - 0.0
  - -0.5
  - -0.8660254
  - 1.01561
  - 0
  - 0
  - 0
  - 1
image_filter:
  crop_filter:
    enable: true
    min_x: 0
    min_y: 0
    max_x: 640
    max_y: 400
  artifact_filter:
    enable: true
    min_depth: 0.3
    max_depth: 3.0
  noise_filter:
    enable: true
point_cloud_filter:
  voxel_filter:
    enable: true
    leaf_size_x: 0.05
    leaf_size_y: 0.05
    leaf_size_z: 0.05
  ground_filter:
    enable: true
    max_z: 0.15
    distance_threshold: 0.05
    angle_threshold: 0.1
  passthrough:
    enable: true
    min_x: 0.5
    max_x: 2.0
    min_y: -1
    max_y: 1
    min_z: -0.5
    max_z: 1.2
point_cloud_publisher_name: /depthcam/processed_cloud
cliff_detector:
  enable: false
  depth_image_step: 2
  range_min: 0.3
  range_max: 3.0
  width_range: 1.0
  height_variance_threshold: 0.03
  depth_image_topic_name: /ascamera_nuwa2/camera_publisher/depth0/image_raw
  camera_info_topic_name: /ascamera_nuwa2/camera_publisher/depth0/camera_info
  cliff_cloud_publisher_name: /depthcam/cliff_cloud
  extrinsics:
  - 0.7547096
  - 0.0
  - 0.656059
  - 0.40698
  - 0.0
  - -1.0
  - 0.0
  - 0
  - 0.656059
  - -0.0
  - -0.7547096
  - 1.01286
  - 0
  - 0
  - 0
  - 1
zl_motor_controller:
  ros__parameters:
    can_id_tx: 1537
    can_id_rx: 1409
    wheel_diameter_left: 0.1388
    wheel_diameter_right: 0.14
    wheel_separation: 0.39
    gear_ratio: 1.0
    encoder_resolution: 16384.0
    odom_frame_id: odom
    base_frame_id: base_link
    publish_tf: true
    cmd_vel_topic: cmd_vel
    odom_topic: odom
    filtered_odom_topic: ''
    can_tx_topic: can_tx
    can_rx_topic: ''
    motor_info_topic: motor_info
    bumper_topic: bumper_state
    check_dispatcher_node: false
    check_status_service: check_node_status
    add_filter_service: add_can_filter
    control_cycle_ms: 25
    print_status_out: false
    status_update_cycle_ms: 1000
    cmd_vel_timeout_ms: 500
    bumper_timeout_ms: 5000
    sdo_response_timeout_ms: 100
    publish_motor_info: false
    use_sockcan_direct: true
    can_interface: can0
    min_send_interval_ms: 2
rplidar_node_front:
  ros__parameters:
    channel_type: udp
    udp_ip: ************
    udp_port: 8089
    frame_id: rplidar_front
    inverted: false
    angle_compensate: true
    scan_mode: UltraDense
    scan_frequency: 15.0
    min_distance: 0.05
rplidar_node_back:
  ros__parameters:
    channel_type: udp
    udp_ip: *************
    udp_port: 8089
    frame_id: rplidar_back
    inverted: false
    angle_compensate: true
    scan_mode: UltraDense
    scan_frequency: 15.0
    min_distance: 0.05
/**:
  ros__parameters:
    robot_base_frame: base_link
    return_to_init: false
    costmap_topic: map
    costmap_updates_topic: map_updates
    visualize: true
    planner_frequency: 0.15
    progress_timeout: 30.0
    potential_scale: 3.0
    orientation_scale: 0.0
    gain_scale: 1.0
    transform_tolerance: 0.3
    min_frontier_size: 0.3
stcm_manager_node:
  ros__parameters:
    map_storage_path: /home/<USER>/maps/
    keep_mask_topic: /keepout_filter_mask
    map_server_topic: /map
robot_monitor_node:
  ros__parameters:
    health_monitor_period: 1.0
    system_monitor_period: 60.0
    disk_usage_warning_threshold: 90.0
    temperature_warning_threshold: 80.0
    enable_disk_monitoring: true
    enable_temperature_monitoring: true
    lidar_topic: /fusion_scan
