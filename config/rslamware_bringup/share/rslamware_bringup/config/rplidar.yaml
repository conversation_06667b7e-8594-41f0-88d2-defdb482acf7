# config/rplidar_params.yaml
rplidar_node_front:
  ros__parameters:
    channel_type: "udp"
    udp_ip: "************"
    udp_port: 8089
    frame_id: "rplidar_front"
    inverted: false
    angle_compensate: true
    scan_mode: "UltraDense"
    scan_frequency: 15.0
    min_distance: 0.05

rplidar_node_back:
  ros__parameters:
    channel_type: "udp"
    udp_ip: "*************"
    udp_port: 8089
    frame_id: "rplidar_back"
    inverted: false
    angle_compensate: true
    scan_mode: "UltraDense"
    scan_frequency: 15.0
    min_distance: 0.05
