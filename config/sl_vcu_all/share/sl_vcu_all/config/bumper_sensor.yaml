bumper_sensor:
  ros__parameters:
    # Input device configuration
    input_device_path: "/dev/input/event2"  # Path to input device
    poll_timeout_ms: 1000                    # Poll timeout in milliseconds
    
    # Bumper event codes
    front_bumper_code: 59                   # Event code for front bumper
    back_bumper_code: 60                    # Event code for back bumper
    triggered_value: 1                      # Event value when bumper is triggered (0 = released)
    
    # Publishing configuration
    publish_rate_ms: 20                     # Rate to publish bumper state (milliseconds)
    bumper_topic: "bumper_state"           # Topic name for bumper state messages
