# LED Display Control Configuration
# This file contains parameters for the LED display control system

led_display_control:
  ros__parameters:
    # Number of LED strip channels
    num_channels: 2

    # Debugging parameters
    debug_mode: false

    # Auto start effect threads for animated modes
    auto_start_effects: true

    # Default effect parameters
    default_frequency: 1.0              # Hz for breathing/flashing effects
    default_speed: 10.0                 # pixels/second for marquee effect
    default_marquee_direction: true     # true=forward, false=reverse
    default_green_brightness: 255       # Default green brightness (0-255)
    default_red_brightness: 255         # Default red brightness (0-255)
    default_blue_brightness: 255        # Default blue brightness (0-255)
    default_on_time_duty: 0.5           # Duty cycle for flashing effect (0.0-1.0)

    # Channel 0 configuration
    channel_0:
      spi_device: "/dev/spidev0.0"      # SPI device path
      spi_speed: 7080000                # SPI clock speed in Hz (6.4MHz for SK6812)
      num_leds: 10                      # Total number of LEDs in this strip
      first_part_leds: 5               # Number of LEDs in first part
      second_part_leds: 5              # Number of LEDs in second part
      enabled: true                     # Enable this channel

    # Channel 1 configuration
    channel_1:
      spi_device: "/dev/spidev3.0"      # SPI device path
      spi_speed: 7080000                # SPI clock speed in Hz
      num_leds: 10                      # Total number of LEDs in this strip
      first_part_leds: 5               # Number of LEDs in first part
      second_part_leds: 5              # Number of LEDs in second part
      enabled: true                     # Enable this channel
