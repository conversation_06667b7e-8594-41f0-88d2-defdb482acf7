# ZL Motor Controller Configuration
# This file contains all configurable parameters for the ZLMotorController node

zl_motor_controller:
  ros__parameters:
    # CAN communication parameters
    can_id_tx: 0x601                    # CAN ID for transmitting commands to motor controller
    can_id_rx: 0x581                    # CAN ID for receiving responses from motor controller

    # Robot physical parameters
    # wheel_diameter: 0.140               # Wheel diameter in meters
    wheel_diameter_left: 0.1388               # Wheel diameter in meters
    wheel_diameter_right: 0.140               # Wheel diameter in meters
    wheel_separation: 0.390             # Distance between wheels in meters
    gear_ratio: 1.0                     # Motor gear ratio
    encoder_resolution: 16384.0         # Encoder ticks per revolution

    # Frame IDs for TF and odometry
    odom_frame_id: "odom"              # Odometry frame ID
    base_frame_id: "base_link"         # Base frame ID
    publish_tf: true                   # Whether to publish TF transforms

    # Topic names
    cmd_vel_topic: "cmd_vel"           # Command velocity topic
    odom_topic: "odom"                 # Odometry topic
    filtered_odom_topic: ""        # Filtered odometry topic (optional)
    can_tx_topic: "can_tx"             # CAN transmit topic (when not using direct sockcan)
    can_rx_topic: ""                   # CAN receive topic (auto-generated if empty)
    motor_info_topic: "motor_info"     # Motor information topic
    bumper_topic: "bumper_state"       # Bumper state topic

    # Service names
    check_dispatcher_node: false        # Whether to check for CAN frame dispatcher node
    check_status_service: "check_node_status"  # Service to check dispatcher node status
    add_filter_service: "add_can_filter"       # Service to add CAN filters

    # Control timing parameters
    control_cycle_ms: 25               # Main control loop cycle time in milliseconds
    print_status_out: false
    status_update_cycle_ms: 1000        # Status update cycle for temperatures and currents in milliseconds

    # Timeout parameters
    cmd_vel_timeout_ms: 500            # Command velocity timeout in milliseconds
    bumper_timeout_ms: 5000            # Bumper state timeout in milliseconds
    sdo_response_timeout_ms: 100      # SDO response timeout in milliseconds

    # Publishing options
    publish_motor_info: false           # Whether to publish motor info messages

    # CAN communication options
    use_sockcan_direct: true           # Use direct SocketCAN instead of ROS topics
    can_interface: "can0"              # SocketCAN interface name
    min_send_interval_ms: 2            # Minimum interval between CAN frame sends in milliseconds
