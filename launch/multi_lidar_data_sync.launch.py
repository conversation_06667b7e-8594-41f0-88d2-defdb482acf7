from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():  
    return LaunchDescription([
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='false',
            description='Use simulation time'),
            
        DeclareLaunchArgument(
            'main_scan_topic',
            default_value='/lidar_front/scan',
            description='Main lidar scan topic'),
        DeclareLaunchArgument(
            'main_lidar_frame',
            default_value='rplidar_front',
            description='Main lidar frame'),
            
        DeclareLaunchArgument(
            'sub_scan_topic',
            default_value='/lidar_back/scan',
            description='Sub-lidar scan topic'),
        DeclareLaunchArgument(
            'sub_lidar_frame',
            default_value='rplidar_back',
            description='Sub-lidar frame'),
            
        DeclareLaunchArgument(
            'scan_pub_topic',
            default_value='/fusion_scan',
            description='Merged scan publish topic'),
            
        DeclareLaunchArgument(
            'odom_frame',
            default_value='odom',
            description='Odometry frame'),
        DeclareLaunchArgument(
            'base_frame',
            default_value='base_link',
            description='Base frame'),
            
        DeclareLaunchArgument(
            'lidar_scan_time_gain',
            default_value='1.0',
            description='Lidar scan time gain'),
        DeclareLaunchArgument(
            'max_sub_lidars',
            default_value='2',
            description='Maximum number of sub-lidars'),

        Node(
            package='multi_lidar_data_sync',
            executable='multi_lidar_data_sync_node',
            name='multi_lidar_data_sync_node',
            parameters=[{
                'main_scan_topic': LaunchConfiguration('main_scan_topic'),
                'main_lidar_frame': LaunchConfiguration('main_lidar_frame'),
            
                'sub_scan_topic': [LaunchConfiguration('sub_scan_topic')],
                'sub_lidar_frame': [LaunchConfiguration('sub_lidar_frame')],
                
                'scan_pub_topic': LaunchConfiguration('scan_pub_topic'),
                
                'odom_frame': LaunchConfiguration('odom_frame'),
                'base_frame': LaunchConfiguration('base_frame'),
                'lidar_scan_time_gain': LaunchConfiguration('lidar_scan_time_gain'),
                'max_sub_lidars': LaunchConfiguration('max_sub_lidars'),
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }],
            output='screen'
        )
    ])