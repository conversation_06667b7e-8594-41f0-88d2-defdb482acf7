# Jack Control Node Configuration
# This file contains all configurable parameters for the Jack Control node

jack_control:
  ros__parameters:
    # Node control parameters
    enable: true                             # Enable jack control node (if false, node will exit without initialization)

    # CAN communication parameters
    can_interface: "can0"                    # SocketCAN interface name

    # Control timing parameters
    control_cycle_ms: 50                     # Control loop cycle time in milliseconds
    response_timeout_ms: 1000                # Timeout for CAN response in milliseconds
    heartbeat_period_ms: 100                  # Heartbeat period for status updates in milliseconds
    min_send_interval_ms: 5                  # Minimum interval between CAN frame sends in milliseconds

    # Jack physical parameters
    default_speed: 1000                      # Default speed in RPM (converted to CAN value: rpm/1875*512*131072)
    max_speed: 3000                          # Maximum allowed speed in RPM (converted to CAN value: rpm/1875*512*131072)
    max_position: 26000000                    # Maximum jack position (encoder counts)
    min_position: 0                          # Minimum jack position (encoder counts)
    position_tolerance: 1000                # Position tolerance for accurate positioning (encoder counts)

    # Safety parameters
    position_timeout_s: 60                   # Timeout for position movements in seconds
    detection_timeout_s: 30                  # Timeout for base detection in seconds
    up_stage_timeout_s: 90                   # Timeout for lifting up stage in seconds
    down_stage_timeout_s: 90                 # Timeout for lifting down stage in seconds
    base_stage_timeout_s: 45                 # Timeout for base detection stage in seconds
    movement_start_delay_s: 3                # Delay before checking position movement has started
    movement_check_tolerance: 100           # Minimum position change to confirm movement started
    status_update_delay_s: 2                 # Delay before checking status flags and position in waitForPositionReached

    # Automatic initialization
    auto_detect_base_on_start: true          # Automatically run base detection on first startup
    
