import launch
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    return LaunchDescription([ 
        DeclareLaunchArgument('health_pub_period', default_value = '0.2', description=''), 

        Node(
            package='robot_health',       
            executable='robot_health_node',      
            output='screen',             
            parameters=[
                {"health_pub_period": LaunchConfiguration('health_pub_period')}
                ],
        ), 

        LogInfo(
            msg="Launch file executed successfully!"
        ),
    ])
