import launch
import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # Get the path to the config file from rslamware_bringup
    config_file = os.path.join(
        get_package_share_directory('rslamware_bringup'),
        'config',
        'rslamware.yaml'
    )

    return LaunchDescription([
        DeclareLaunchArgument(
            'config_file',
            default_value=config_file,
            description='Path to robot monitor configuration file'
        ),

        Node(
            package='robot_monitor',
            executable='robot_monitor_node',
            name='robot_monitor_node',
            output='screen',
            parameters=[LaunchConfiguration('config_file')],
        ),

        LogInfo(
            msg="Robot monitor launched with HealthProvider integration!"
        ),
    ])
