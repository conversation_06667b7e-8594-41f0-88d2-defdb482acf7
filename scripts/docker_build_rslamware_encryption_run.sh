#!/bin/bash

# RSlamware Encryption Docker Build Script
# This script is designed to run inside the Docker container

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

# Function to print colored output
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

print_status "=========================================="
print_status "RSlamware Encryption Docker Build Script"
print_status "=========================================="
print_status "Workspace: $RSLAMWARE_ROOT"
print_status "=========================================="
echo ""

# Check if we're in the correct directory
if [ ! -f "$RSLAMWARE_ROOT/src/rslamware_encryption_run/CMakeLists.txt" ]; then
    print_error "rslamware_encryption_run package not found!"
    print_error "Please make sure you're running this script from the correct workspace."
    exit 1
fi

# Change to workspace directory
cd "$RSLAMWARE_ROOT"

# Step 1: Clean previous build
print_status "Step 1: Cleaning previous build..."
# rm -rf build install log rslamware.tar.gz rslamware.enc 2>/dev/null || true
rm -rf rslamware.tar.gz rslamware.enc 2>/dev/null || true

# Step 2: Source ROS2 environment
print_status "Step 2: Sourcing ROS2 environment..."
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    print_status "ROS2 Humble environment sourced."
else
    print_error "ROS2 Humble not found! Please install ROS2 Humble."
    exit 1
fi

# Step 3: Build all packages
print_status "Step 3: Building all packages..."
print_status "Running: colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release"

colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    print_error "Build failed!"
    exit 1
fi

print_status "Build completed successfully!"
echo ""

# Step 4: Source the workspace
print_status "Step 4: Sourcing workspace..."
if [ -f "install/setup.bash" ]; then
    source install/setup.bash
    print_status "Workspace sourced successfully."
else
    print_error "Workspace setup file not found!"
    exit 1
fi

# Step 5: Run preparation script
print_status "Step 5: Running preparation script..."
print_status "Executing: python3 src/rslamware_encryption_run/src/rslamware_encryption_prepare.py"

python3 src/rslamware_encryption_run/src/rslamware_encryption_prepare.py

if [ $? -ne 0 ]; then
    print_error "Preparation script failed!"
    exit 1
fi

print_status "Preparation completed successfully!"
echo ""

# Step 6: Encrypt the archive
print_status "Step 6: Encrypting the archive..."

if [ ! -f "rslamware.tar.gz" ]; then
    print_error "Archive file rslamware.tar.gz not found!"
    exit 1
fi

ENCRYPTION_TOOL="install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run"

if [ ! -f "$ENCRYPTION_TOOL" ]; then
    print_error "Encryption tool not found: $ENCRYPTION_TOOL"
    exit 1
fi

print_status "Running: $ENCRYPTION_TOOL --encrypt rslamware.tar.gz rslamware.enc"

"$ENCRYPTION_TOOL" --encrypt rslamware.tar.gz rslamware.enc

if [ $? -ne 0 ]; then
    print_error "Encryption failed!"
    exit 1
fi

print_status "Encryption completed successfully!"
echo ""

# Step 7: Verify encrypted file
if [ -f "rslamware.enc" ]; then
    ENCRYPTED_SIZE=$(stat -c%s "rslamware.enc")
    print_status "Encrypted file created: rslamware.enc (${ENCRYPTED_SIZE} bytes)"
else
    print_error "Encrypted file not found!"
    exit 1
fi

# Step 8: Clean up intermediate files
print_status "Step 7: Cleaning up intermediate files..."
# rm -f rslamware.tar.gz

print_status "=========================================="
print_status "Build and encryption completed successfully!"
print_status "=========================================="
print_status "Encrypted file: rslamware.enc"
print_status "Run scripts available:"
print_status "  - scripts/run_rslamware_encryption_run.sh"
print_status "  - scripts/run_rslamware_encryption_run_simulator.sh"
print_status "=========================================="
echo ""

print_status "To test the encrypted system, run:"
print_status "  ./scripts/run_rslamware_encryption_run.sh mapping"
print_status "  ./scripts/run_rslamware_encryption_run.sh localization"
print_status "  ./scripts/run_rslamware_encryption_run_simulator.sh mapping"
