#!/bin/bash

# RSlamware Encrypted Simulator Execution Script
# This script runs the encrypted rslamware system in simulation mode for testing

SELF="$(which $0)"
SCRIPTS_ROOT="`realpath $(dirname ${SELF})`"
RSLAMWARE_ROOT="$SCRIPTS_ROOT/.."

# Simulator specific settings (from original run_simulator.sh)
simulator_map="$RSLAMWARE_ROOT/src/simulator/map/map.yaml" 
simulator_map_pbstream="$RSLAMWARE_ROOT/src/simulator/map/map.pbstream"
mask_file="$RSLAMWARE_ROOT/src/simulator/map/map.yaml"

mapping=False
use_als=False
use_cartographer_localization=True
explore=False
scan_topic="scan"
no_gui=False

# Function to print colored output
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# Parse command line arguments
for arg in "$@"
do
    if [ "$arg" == "mapping" ]; then
        mapping=True
    fi
    if [ "$arg" == "use_als" ]; then
        use_als=True
        use_cartographer_localization=False
    fi
    if [ "$arg" == "use_cartographer_localization" ]; then
        use_cartographer_localization=True
        use_als=False
    fi
    if [ "$arg" == "explore" ]; then
        explore=True
    fi
    if [ "$arg" == "no_gui" ]; then
        no_gui=True
    fi
done

print_status "=========================================="
print_status "RSlamware Encrypted Simulator Execution"
print_status "=========================================="
print_status "Workspace: $RSLAMWARE_ROOT"
print_status "Mode: $([ "$mapping" == "True" ] && echo "MAPPING" || echo "LOCALIZATION")"
print_status "Scan topic: $scan_topic"
print_status "Use ALS: $use_als"
print_status "Use Cartographer Localization: $use_cartographer_localization"
print_status "Explore: $explore"
print_status "No GUI: $no_gui"
print_status "=========================================="
echo ""

# Check if encrypted file exists
if [ ! -f "$RSLAMWARE_ROOT/rslamware.enc" ]; then
    print_error "Encrypted file not found: $RSLAMWARE_ROOT/rslamware.enc"
    print_error "Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

# Check if encryption tool exists
ENCRYPTION_TOOL="$RSLAMWARE_ROOT/install/rslamware_encryption_run/lib/rslamware_encryption_run/rslamware_encryption_run"
if [ ! -f "$ENCRYPTION_TOOL" ]; then
    print_error "Encryption tool not found: $ENCRYPTION_TOOL"
    print_error "Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

# Source ROS2 environment
print_status "Sourcing ROS2 environment..."
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
else
    print_error "ROS2 Humble not found! Please install ROS2 Humble."
    exit 1
fi

# Source workspace
if [ -f "$RSLAMWARE_ROOT/install/setup.bash" ]; then
    source "$RSLAMWARE_ROOT/install/setup.bash"
else
    print_error "Workspace not built! Please run build_rslamware_encryption_run.sh first."
    exit 1
fi

print_status "Environment sourced successfully."
echo ""

# Change to workspace directory
cd "$RSLAMWARE_ROOT"

# Note: This is a simplified version for testing
# In a real implementation, you would need to modify the C++ tool to support simulation mode
# For now, we'll run the same encrypted system but with simulation parameters

print_status "Starting encrypted rslamware simulator system..."
print_warning "Note: This is a test version. Full simulator integration requires additional development."

if [ "$mapping" == "True" ]; then
    print_status "Running in SIMULATION MAPPING mode..."
    # For testing, we'll use the same tool but note that it needs simulation mode support
    "$ENCRYPTION_TOOL" --run --mapping
else
    print_status "Running in SIMULATION LOCALIZATION mode..."
    "$ENCRYPTION_TOOL" --run --localization
fi

# Start RViz if GUI is enabled
if [ "$no_gui" != "True" ]; then
    print_status "Starting RViz..."
    if [ -f "$RSLAMWARE_ROOT/src/bringup/rviz/nav2_default_view.rviz" ]; then
        ros2 run rviz2 rviz2 -d "$RSLAMWARE_ROOT/src/bringup/rviz/nav2_default_view.rviz" &
        RVIZ_PID=$!
        print_status "RViz started with PID: $RVIZ_PID"
    else
        print_warning "RViz config file not found, skipping RViz startup"
    fi
fi

if [ $? -eq 0 ]; then
    print_status "Encrypted rslamware simulator execution completed successfully!"
else
    print_error "Encrypted rslamware simulator execution failed!"
    exit 1
fi
