#!/usr/bin/env python3

"""
Main launch file for the SL VCU All package.
This launch file starts all the essential components:
- ZL Motor Controller
- IMU Sensor with bias correction and yaw integration
- Robot Localization EKF for sensor fusion
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    # Get package directory
    pkg_share = FindPackageShare('sl_vcu_all')
    
    # Declare launch arguments
    motor_config_file_arg = DeclareLaunchArgument(
        'motor_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'zl_motor_controller.yaml'
        ]),
        description='Path to the motor controller configuration file'
    )
    
    imu_config_file_arg = DeclareLaunchArgument(
        'imu_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'imu_sensor.yaml'
        ]),
        description='Path to the IMU sensor configuration file'
    )

    bumper_config_file_arg = DeclareLaunchArgument(
        'bumper_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'bumper_sensor.yaml'
        ]),
        description='Path to the bumper sensor configuration file'
    )

    jack_config_file_arg = DeclareLaunchArgument(
        'jack_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'jack_control.yaml'
        ]),
        description='Path to the jack control configuration file'
    )

    led_config_file_arg = DeclareLaunchArgument(
        'led_config_file',
        default_value=PathJoinSubstitution([
            pkg_share,
            'config',
            'led_control.yaml'
        ]),
        description='Path to LED control configuration file'
    )
    
    log_level_arg = DeclareLaunchArgument(
        'log_level',
        default_value='info',
        description='Log level for all nodes (debug, info, warn, error)'
    )
    
    odom_topic_arg = DeclareLaunchArgument(
        'odom_topic',
        default_value='odom',
        description='Odometry topic name for EKF output'
    )
    
    enable_ekf_arg = DeclareLaunchArgument(
        'enable_ekf',
        default_value='true',
        description='Enable robot localization EKF (true/false)'
    )

    imu_publish_tf_arg = DeclareLaunchArgument(
        'imu_publish_tf',
        default_value='true',
        description='Enable IMU TF publishing (true/false)'
    )

    imu_parent_frame_arg = DeclareLaunchArgument(
        'imu_parent_frame',
        default_value='base_link',
        description='Parent frame for IMU TF (e.g., base_link, odom)'
    )

    imu_child_frame_arg = DeclareLaunchArgument(
        'imu_child_frame',
        default_value='imu_link',
        description='Child frame for IMU TF (should match imu_frame_id)'
    )
    
    # ZL Motor Controller Node
    zl_motor_controller_node = Node(
        package='sl_vcu_all',
        executable='zl_motor_controller_node',
        name='zl_motor_controller',
        parameters=[LaunchConfiguration('motor_config_file')],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # IMU Sensor Node
    imu_sensor_node = Node(
        package='sl_vcu_all',
        executable='imu_sensor_node',
        name='imu_sensor',
        parameters=[
            LaunchConfiguration('imu_config_file'),
            {
                'publish_tf': LaunchConfiguration('imu_publish_tf'),
                'parent_frame_id': LaunchConfiguration('imu_parent_frame'),
                'child_frame_id': LaunchConfiguration('imu_child_frame')
            }
        ],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # Bumper Sensor Node
    bumper_sensor_node = Node(
        package='sl_vcu_all',
        executable='bumper_sensor_node',
        name='bumper_sensor',
        parameters=[
            LaunchConfiguration('bumper_config_file')
        ],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # Jack Control Node
    jack_control_node = Node(
        package='sl_vcu_all',
        executable='jack_control_node',
        name='jack_control',
        parameters=[
            LaunchConfiguration('jack_config_file')
        ],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # LED Display Control Node
    led_display_control_node = Node(
        package='sl_vcu_all',
        executable='led_display_control_node',
        name='led_display_control',
        parameters=[
            LaunchConfiguration('led_config_file')
        ],
        output='screen',
        arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')]
    )

    # Robot Localization EKF Node (with delay to ensure IMU is ready)
    # robot_localization_node = TimerAction(
    #     period=2.0,  # Wait 2 seconds for IMU to initialize
    #     actions=[
    #         Node(
    #             package='robot_localization',
    #             executable='ekf_node',
    #             name='ekf_filter_node',
    #             output='screen',
    #             parameters=[LaunchConfiguration('ekf_config_file')],
    #             remappings=[
    #                 ('/odometry/filtered', LaunchConfiguration('odom_topic')),
    #             ],
    #             arguments=['--ros-args', '--log-level', LaunchConfiguration('log_level')],
    #             condition=IfCondition(LaunchConfiguration('enable_ekf'))
    #         )
    #     ]
    # )
    
    # Optional: Add a static transform publisher for base_link to imu_link
    # This is needed if your IMU frame is different from base_link
    # static_transform_publisher = Node(
    #     package='tf2_ros',
    #     executable='static_transform_publisher',
    #     name='base_to_imu_transform',
    #     arguments=['0', '0', '0', '0', '0', '0', 'base_link', 'imu_link'],
    #     output='screen'
    # )
    
    # Optional: Add a diagnostic aggregator for monitoring system health
    # diagnostic_aggregator = Node(
    #     package='diagnostic_aggregator',
    #     executable='aggregator_node',
    #     name='diagnostic_aggregator',
    #     parameters=[{
    #         'analyzers': {
    #             'motors': {
    #                 'type': 'diagnostic_aggregator/GenericAnalyzer',
    #                 'path': 'Motors',
    #                 'find_and_remove_prefix': ['zl_motor_controller']
    #             },
    #             'sensors': {
    #                 'type': 'diagnostic_aggregator/GenericAnalyzer', 
    #                 'path': 'Sensors',
    #                 'find_and_remove_prefix': ['imu_sensor']
    #             },
    #             'localization': {
    #                 'type': 'diagnostic_aggregator/GenericAnalyzer',
    #                 'path': 'Localization', 
    #                 'find_and_remove_prefix': ['ekf_filter_node']
    #             }
    #         }
    #     }],
    #     output='screen',
    #     condition=IfCondition(LaunchConfiguration('enable_ekf'))
    # )

    return LaunchDescription([
        # Launch arguments
        motor_config_file_arg,
        imu_config_file_arg,
        bumper_config_file_arg,
        jack_config_file_arg,
        led_config_file_arg,
        # ekf_config_file_arg,
        log_level_arg,
        odom_topic_arg,
        enable_ekf_arg,
        imu_publish_tf_arg,
        imu_parent_frame_arg,
        imu_child_frame_arg,
        
        # Core nodes

        imu_sensor_node,
        zl_motor_controller_node,

        # robot_localization_node,

        bumper_sensor_node,
        jack_control_node,
        led_display_control_node,

        # Supporting nodes
        # static_transform_publisher,
        # diagnostic_aggregator,  # Uncomment if diagnostic_aggregator is available
    ])


if __name__ == '__main__':
    generate_launch_description()
